<?php

use think\facade\Route;
use yunwuxin\auth\middleware\UseGuard;

Route::domain('*', function () {
    Route::get('/', 'index/index');
    Route::post('/', 'index/open');

    Route::redirect('preview', '/preview/');

    Route::get('preview/:path', 'preview/index')->pattern(['path' => '.*']);

    Route::get('download', 'download/index');

    Route::put('asset/:path', 'asset/upload')->pattern(['path' => '.*']);
    Route::rule('asset/:path', 'asset/download', 'GET|HEAD')->pattern(['path' => '.*']);
    Route::get('asset', 'asset/index');

    Route::get('assistant', 'assistant.index/index');
    Route::post('assistant/chat', 'assistant.index/chat');
    Route::get('assistant/conversation', 'assistant.conversation/index');
    Route::delete('assistant/conversation/:id', 'assistant.conversation/delete');
    Route::post('assistant/mcp/tools', 'assistant.mcp/tools');

    Route::put('import/file', 'import/file');
    Route::post('import/:id', 'import/save');

    Route::post('export/:id', 'export/save');
    Route::get('export/:id', 'export/download');
})->prefix('space.')->middleware(UseGuard::class, 'space');
