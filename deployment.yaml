apiVersion: apps/v1
kind: Deployment
metadata:
  name: x
  namespace: topwrite
  labels:
    app: x
spec:
  replicas: 1
  selector:
    matchLabels:
      app: x
  template:
    metadata:
      labels:
        app: x
    spec:
      initContainers:
        - name: x-init
          image: registry-vpc.cn-shanghai.aliyuncs.com/topthink/write-x:IMAGE_TAG
          imagePullPolicy: Always
          args:
            - 'app:init'
          env:
            - name: PHP_DB_NAME
              valueFrom:
                configMapKeyRef:
                  name: x-config
                  key: db-name
            - name: PHP_DB_HOST
              valueFrom:
                configMapKeyRef:
                  name: x-config
                  key: db-host
            - name: PHP_DB_USER
              valueFrom:
                configMapKeyRef:
                  name: x-config
                  key: db-user
            - name: PHP_DB_PASS
              valueFrom:
                configMapKeyRef:
                  name: x-config
                  key: db-pass
          resources:
            requests:
              cpu: 250m
              memory: 512Mi
      containers:
        - name: x-main
          image: registry-vpc.cn-shanghai.aliyuncs.com/topthink/write-x:IMAGE_TAG
          imagePullPolicy: Always
          env:
            - name: PHP_CACHE_TYPE
              value: redis
            - name: PHP_DB_NAME
              valueFrom:
                configMapKeyRef:
                  name: x-config
                  key: db-name
            - name: PHP_DB_HOST
              valueFrom:
                configMapKeyRef:
                  name: x-config
                  key: db-host
            - name: PHP_DB_USER
              valueFrom:
                configMapKeyRef:
                  name: x-config
                  key: db-user
            - name: PHP_DB_PASS
              valueFrom:
                configMapKeyRef:
                  name: x-config
                  key: db-pass
            - name: PHP_REDIS_HOST
              valueFrom:
                configMapKeyRef:
                  name: common-config
                  key: redis-host
            - name: PHP_PROXY
              valueFrom:
                configMapKeyRef:
                  name: x-config
                  key: proxy
            - name: PHP_FEEDBACK_URL
              valueFrom:
                configMapKeyRef:
                  name: x-config
                  key: feedback-url
            - name: PHP_CLOUD_HOST
              valueFrom:
                configMapKeyRef:
                  name: x-config
                  key: cloud-host
            - name: PHP_CLOUD_CLIENT_ID
              valueFrom:
                configMapKeyRef:
                  name: x-config
                  key: cloud-client-id
            - name: PHP_CLOUD_CLIENT_SECRET
              valueFrom:
                configMapKeyRef:
                  name: x-config
                  key: cloud-client-secret
            - name: PHP_WIKI_HOST
              valueFrom:
                configMapKeyRef:
                  name: x-config
                  key: wiki-host
            - name: PHP_WIKI_CLIENT_ID
              valueFrom:
                configMapKeyRef:
                  name: x-config
                  key: wiki-client-id
            - name: PHP_WIKI_CLIENT_SECRET
              valueFrom:
                configMapKeyRef:
                  name: x-config
                  key: wiki-client-secret
            - name: PHP_TRACING_TYPE
              value: topwrite-x
            - name: PHP_ZIPKIN_ENDPOINT
              value: http://tracing-analysis-dc-sh-internal.aliyuncs.com/adapt_hvfmcpk6dx@82da6cbc6ee1477_hvfmcpk6dx@53df7ad2afe8301/api/v2/spans
            - name: aliyun_logs_topwrite-x
              value: /opt/htdocs/runtime/log/*.log
            - name: aliyun_logs_topwrite-x_project
              value: topthink
          resources:
            requests:
              cpu: 250m
              memory: 512Mi
          ports:
            - name: http
              containerPort: 80
              protocol: TCP
            - name: rpc
              containerPort: 9000
              protocol: TCP
          volumeMounts:
            - name: volume-storage
              mountPath: /opt/repo
            - name: volume-book
              mountPath: /opt/htdocs/runtime/storage
            - name: volume-log
              mountPath: /opt/htdocs/runtime/log
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 15
            periodSeconds: 10
            successThreshold: 1
            tcpSocket:
              port: 8080
            timeoutSeconds: 1
          startupProbe:
            failureThreshold: 3
            initialDelaySeconds: 15
            periodSeconds: 10
            successThreshold: 1
            tcpSocket:
              port: 8080
            timeoutSeconds: 1
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 15
            periodSeconds: 10
            successThreshold: 1
            tcpSocket:
              port: 8080
            timeoutSeconds: 1
      volumes:
        - name: volume-log
          emptyDir: { }
        - name: volume-storage
          persistentVolumeClaim:
            claimName: repo
        - name: volume-book
          persistentVolumeClaim:
            claimName: book
      serviceAccountName: release-manager
---
apiVersion: v1
kind: Service
metadata:
  name: x-svc
  namespace: topwrite
spec:
  ports:
    - name: http
      port: 80
      protocol: TCP
      targetPort: 80
    - name: rpc
      port: 9000
      protocol: TCP
      targetPort: 9000
  selector:
    app: x
  type: ClusterIP
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: release-manager
  namespace: topwrite
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: release-manager
  namespace: topwrite
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: cluster-admin
subjects:
  - kind: ServiceAccount
    name: release-manager
    namespace: topwrite
