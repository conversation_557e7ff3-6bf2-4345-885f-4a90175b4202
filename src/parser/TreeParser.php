<?php

namespace topthink\git\parser;

class TreeParser extends ParserBase
{
    public $entries = [];

    protected function doParse()
    {
        while (!$this->isFinished()) {
            $vars = $this->consumeRegexp('/\d{6}/A');
            $mode = $vars[0];
            $this->consume(' ');

            $vars = $this->consumeRegexp('/(blob|tree|commit)/A');
            $type = $vars[0];
            $this->consume(' ');

            $hash = $this->consumeHash();
            $this->consume("\t");

            $name = $this->consumeTo("\n");
            $this->consumeNewLine();

            $this->entries[] = [$mode, $type, $hash, $name];
        }
    }
}
