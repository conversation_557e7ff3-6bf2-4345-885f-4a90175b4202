<?php

namespace topthink\git\parser;

use LogicException;

class StatusParser extends ParserBase
{
    public $files;
    public $branch;

    protected function doParse()
    {
        $branch = [];
        $this->consume('# branch.oid ');
        $branch['oid'] = $this->consumeTo("\n");
        $this->consumeNewLine();

        $this->consume('# branch.head ');
        $branch['head'] = $this->consumeTo("\n");
        $this->consumeNewLine();

        if ($this->expects('# branch.upstream ')) {
            $branch['upstream'] = $this->consumeTo("\n");
            $this->consumeNewLine();
            if ($this->expects('# branch.ab ')) {
                $ab = [];
                $this->consume('+');
                $ab[0] = (int) $this->consumeTo(' ');
                $this->consumeSpace();
                $this->consume('-');
                $ab[1] = (int) $this->consumeTo("\n");

                $branch['ab'] = $ab;
                $this->consumeNewLine();
            } else {
                $branch['ab'] = [0, 0];
            }
        }

        $this->branch = $branch;

        $files = [];
        while (!$this->isFinished()) {
            if ($this->expects('u')) {
                $type = 'u';
                //unmerged
                $this->consumeSpace();
                $x = $this->consumeLength(1);
                $y = $this->consumeLength(1);
                $this->consumeSpace();
                $this->consume('N...');
                $this->consumeSpace();
                $this->consumeLength(6);
                $this->consumeSpace();
                $this->consumeLength(6);
                $this->consumeSpace();
                $this->consumeLength(6);
                $this->consumeSpace();
                $this->consumeLength(6);
                $this->consumeSpace();
                $this->consumeLength(40);
                $this->consumeSpace();
                $this->consumeLength(40);
                $this->consumeSpace();
                $this->consumeLength(40);
                $this->consumeSpace();
                $filename = $this->consumeTo("\n");
                $this->consumeNewLine();
            } elseif ($this->expects('1')) {
                $type = '1';
                //changed
                $this->consumeSpace();
                $x = $this->consumeLength(1);
                $y = $this->consumeLength(1);
                $this->consumeSpace();
                $this->consume('N...');
                $this->consumeSpace();
                $this->consumeLength(6);
                $this->consumeSpace();
                $this->consumeLength(6);
                $this->consumeSpace();
                $this->consumeLength(6);
                $this->consumeSpace();
                $this->consumeLength(40);
                $this->consumeSpace();
                $this->consumeLength(40);
                $this->consumeSpace();
                $filename = $this->consumeTo("\n");
                $this->consumeNewLine();
            } elseif ($this->expects('?')) {
                $type = '?';
                $x    = '?';
                $y    = '?';
                $this->consumeSpace();
                $filename = $this->consumeTo("\n");
                $this->consumeNewLine();
            } else {
                throw new LogicException('unsupported type');
            }

            $files[] = [
                'filename' => $filename, 'type' => $type, 'x' => $x ?? null, 'y' => $y ?? null,
            ];
        }
        $this->files = $files;
    }
}
