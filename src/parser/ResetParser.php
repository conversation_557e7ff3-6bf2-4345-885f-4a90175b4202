<?php

namespace topthink\git\parser;

class ResetParser extends ParserBase
{
    public $files = [];

    protected function doParse()
    {
        $this->files = [];
        if (!$this->expects("Unstaged changes after reset:")) {
            return;
        }
        $this->consumeNewLine();
        while (!$this->isFinished()) {
            if ($this->consumeRegexp("/[A-Z]\t/")) {
                $this->files[] = $this->consumeTo("\n");
            } else {
                break;
            }
            $this->consumeNewLine();
        }
    }
}
