<?php

namespace topthink\git\parser;

class ReferenceParser extends ParserBase
{
    public $references;

    protected function doParse()
    {
        $this->references = [];

        while (!$this->isFinished()) {
            $hash = $this->consumeHash();
            $this->consume(' ');
            $name = $this->consumeTo("\n");
            $this->consumeNewLine();
            $this->references[] = [$hash, $name];
        }
    }
}
