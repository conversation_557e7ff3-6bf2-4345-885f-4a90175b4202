<?php

namespace topthink\git\diff;

use topthink\git\parser\DiffParser;
use topthink\git\Repository;

class Diff
{
    /**
     * @var array
     */
    protected $files;

    /**
     * @var string
     */
    protected $rawDiff;

    /**
     * Constructs a new diff for a given revision.
     *
     * @param array $files The files
     * @param string $rawDiff The raw diff
     */
    public function __construct(array $files, $rawDiff)
    {
        $this->files   = $files;
        $this->rawDiff = $rawDiff;
    }

    /**
     * @param $rawDiff
     * @return Diff
     */
    public static function parse($rawDiff)
    {
        $parser = new DiffParser();
        $parser->parse($rawDiff);

        return new self($parser->files, $rawDiff);
    }

    public function setRepository(Repository $repository)
    {
        foreach ($this->files as $file) {
            $file->setRepository($repository);
        }
    }

    /**
     * Get list of files modified in the diff's revision.
     *
     * @return array An array of Diff\File objects
     */
    public function getFiles()
    {
        return $this->files;
    }

    /**
     * Returns the raw diff.
     *
     * @return string The raw diff
     */
    public function getRawDiff()
    {
        return $this->rawDiff;
    }

    /**
     * Export a diff as array.
     *
     * @return array The array
     */
    public function toArray()
    {
        return [
            'rawDiff' => $this->rawDiff,
            'files'   => array_map(
                function (File $file) {
                    return $file->toArray();
                },
                $this->files
            ),
        ];
    }

    /**
     * Create a new instance of Diff from an array.
     *
     * @param array $array The array
     *
     * @return Diff The new instance
     */
    public static function fromArray(array $array)
    {
        return new static(
            array_map(
                function ($array) {
                    return File::fromArray($array);
                },
                $array['files']
            ),
            $array['rawDiff']
        );
    }
}
