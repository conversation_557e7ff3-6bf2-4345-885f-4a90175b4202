<?php

namespace topthink\git;

class Revision
{
    /**
     * @var Repository
     */
    protected $repository;

    /**
     * @var string
     */
    protected $revision;

    public function __construct(Repository $repository, $revision)
    {
        $this->repository = $repository;
        $this->revision   = $revision;
    }

    /**
     * @param null $paths
     * @param null $offset
     * @param null $limit
     * @return Log
     */
    public function getLog($paths = null, $offset = null, $limit = null)
    {
        return $this->repository->getLog($this, $paths, $offset, $limit);
    }

    /**
     * Returns the last modification date of the reference.
     *
     * @return Commit
     */
    public function getCommit()
    {
        return $this->getLog()->getSingleCommit();
    }

    /**
     * @return string
     */
    public function getRevision()
    {
        return $this->revision;
    }

    /**
     * @return Repository
     */
    public function getRepository()
    {
        return $this->repository;
    }
}
