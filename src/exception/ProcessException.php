<?php

namespace topthink\git\exception;

use Symfony\Component\Process\Process;

class ProcessException extends RuntimeException implements GitExceptionInterface
{
    protected $process;

    public function __construct(Process $process)
    {
        $error = sprintf(
            'Error while running git command (%s).' . "\nExit Code: %s(%s)\nWorking directory: %s",
            $process->getCommandLine(),
            $process->getExitCode(),
            $process->getExitCodeText(),
            $process->getWorkingDirectory()
        );

        if (!$process->isOutputDisabled()) {
            $error .= sprintf("\nOutput:\n================\n%s\nError Output:\n================\n%s",
                $process->getOutput(),
                $process->getErrorOutput()
            );
        }

        parent::__construct($error);

        $this->process = $process;
    }

    public function getProcess()
    {
        return $this->process;
    }

    public function getExitCode()
    {
        return $this->process->getExitCode();
    }

    public function getErrorOutput()
    {
        return $this->process->getErrorOutput();
    }

    public function getOutput()
    {
        return $this->process->getOutput();
    }
}
