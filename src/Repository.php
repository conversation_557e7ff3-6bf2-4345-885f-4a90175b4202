<?php

namespace topthink\git;

use FilesystemIterator;
use RecursiveDirectoryIterator;
use RecursiveIteratorIterator;
use Symfony\Component\Process\Process;
use topthink\git\diff\Diff;
use topthink\git\exception\InvalidArgumentException;
use topthink\git\exception\ProcessException;
use topthink\git\exception\RuntimeException;

class Repository
{
    public const DEFAULT_DESCRIPTION = "Unnamed repository; edit this file 'description' to name the repository.\n";

    public const EMPTY_TREE_SHA1 = '4b825dc642cb6eb9a060e54bf8d69288fbee4904';

    protected static $listeners = [];

    /**
     * Directory containing git files.
     *
     * @var string
     */
    protected $gitDir;

    /**
     * Working directory.
     *
     * @var string
     */
    protected $workingDir;

    /**
     * Cache containing all objects of the repository.
     *
     * Associative array, indexed by object hash
     *
     * @var array
     */
    protected $objects = [];

    /**
     * Reference bag associated to this repository.
     *
     * @var ReferenceBag
     */
    protected $referenceBag;

    /**
     * Path to git command.
     */
    protected $command = 'git';

    /**
     * Environment that should be set for every running process.
     *
     * @var array
     */
    protected $environment = [];

    /**
     * Timeout that should be set for every running process.
     *
     * @var int
     */
    protected $timeout = 3600;

    protected $lock = null;

    /**
     * Constructs a new repository.
     *
     * Available options are:
     *
     * * working_dir : specify where working copy is located (option --work-tree)
     *
     * * environment : define environment for every ran process
     *
     * @param string $dir path to git repository
     *
     * @throws InvalidArgumentException The folder does not exists
     */
    public function __construct($dir, $withLock = false)
    {
        $this->initDir($dir);

        if ($withLock) {
            $lockFile = $this->gitDir . '/LOCK';

            $this->lock = fopen($lockFile, 'c');
        }
    }

    /**
     * Initializes directory attributes on repository:.
     *
     * @param string $gitDir directory of a working copy with files checked out
     * @param string $workingDir directory containing git files (objects, config...)
     */
    private function initDir($gitDir, $workingDir = null)
    {
        $realGitDir = realpath($gitDir);

        if (false === $realGitDir) {
            throw new InvalidArgumentException(sprintf('Directory "%s" does not exist or is not a directory', $gitDir));
        } elseif (!is_dir($realGitDir)) {
            throw new InvalidArgumentException(sprintf('Directory "%s" does not exist or is not a directory', $realGitDir));
        } elseif (null === $workingDir && is_dir($realGitDir . '/.git')) {
            $workingDir = $realGitDir;
            $realGitDir = $realGitDir . '/.git';
        }

        $this->gitDir     = $realGitDir;
        $this->workingDir = $workingDir;
    }

    /**
     * Tests if repository is a bare repository.
     *
     * @return bool
     */
    public function isBare()
    {
        return null === $this->workingDir;
    }

    /**
     * Returns the HEAD resolved as a commit.
     *
     * @return Commit|null returns a Commit or ``null`` if repository is empty
     */
    public function getHeadCommit()
    {
        $head = $this->getHead();

        if ($head instanceof Reference) {
            return $head->getCommit();
        }

        return $head;
    }

    /**
     * @return Reference|Commit|null current HEAD object or null if error occurs
     * @throws RuntimeException Unable to find file HEAD (debug-mode only)
     *
     */
    public function getHead()
    {
        $file = $this->gitDir . '/HEAD';

        if (!file_exists($file)) {
            $message = sprintf('Unable to find HEAD file ("%s")', $file);
            throw new RuntimeException($message);
        }

        $content = trim(file_get_contents($file));

        if (preg_match('/^ref: (.+)$/', $content, $vars)) {
            return $this->getReferences()->get($vars[1]);
        } elseif (preg_match('/^[0-9a-f]{40}$/', $content)) {
            return $this->getCommit($content);
        }

        $message = sprintf('Unexpected HEAD file content (file: %s). Content of file: %s', $file, $content);

        throw new RuntimeException($message);
    }

    /**
     * @return bool
     */
    public function isHeadDetached()
    {
        return !$this->isHeadAttached();
    }

    /**
     * @return bool
     */
    public function isHeadAttached()
    {
        return $this->getHead() instanceof Reference;
    }

    /**
     * Returns the path to the git repository.
     *
     * @return string A directory path
     */
    public function getPath()
    {
        return $this->workingDir === null ? $this->gitDir : $this->workingDir;
    }

    /**
     * Returns the directory containing git files (git-dir).
     *
     * @return string
     */
    public function getGitDir()
    {
        return $this->gitDir;
    }

    /**
     * Returns the work-tree directory. This may be null if repository is
     * bare.
     *
     * @return string path to repository or null if repository is bare
     */
    public function getWorkingDir()
    {
        return $this->workingDir;
    }

    /**
     * Instanciates a revision.
     *
     * @param string $name Name of the revision
     *
     * @return Revision
     */
    public function getRevision($name)
    {
        return new Revision($this, $name);
    }

    /**
     * Returns the reference list associated to the repository.
     *
     * @param bool $reload Reload references from the filesystem
     *
     * @return ReferenceBag
     */
    public function getReferences($reload = true)
    {
        if (null === $this->referenceBag || $reload) {
            $this->referenceBag = new ReferenceBag($this);
        }

        return $this->referenceBag;
    }

    public function getCurrentBranch()
    {
        return trim($this->run('branch', ['--show-current']));
    }

    /**
     * Instanciates a commit object or fetches one from the cache.
     *
     * @param string $hash A commit hash, with a length of 40
     *
     * @return Commit
     */
    public function getCommit($hash)
    {
        if (!isset($this->objects[$hash])) {
            $this->objects[$hash] = new Commit($this, $hash);
        }

        return $this->objects[$hash];
    }

    /**
     * Instanciates a tree object or fetches one from the cache.
     *
     * @param string $hash A tree hash, with a length of 40
     *
     * @return Tree
     */
    public function getTree($hash)
    {
        if (!isset($this->objects[$hash])) {
            $this->objects[$hash] = new Tree($this, $hash);
        }

        return $this->objects[$hash];
    }

    /**
     * Instanciates a blob object or fetches one from the cache.
     *
     * @param string $hash A blob hash, with a length of 40
     *
     * @return Blob
     */
    public function getBlob($hash)
    {
        if (!isset($this->objects[$hash])) {
            $this->objects[$hash] = new Blob($this, $hash);
        }

        return $this->objects[$hash];
    }

    public function getBlame($revision, $file, $lineRange = null)
    {
        if (is_string($revision)) {
            $revision = $this->getRevision($revision);
        }

        return new Blame($this, $revision, $file, $lineRange);
    }

    /**
     * Returns log for a given set of revisions and paths.
     *
     * All those values can be null, meaning everything.
     *
     * @param array $revisions An array of revisions to show logs from. Can be
     *                         any text value type
     * @param array $paths Restrict log to modifications occurring on given
     *                         paths.
     * @param int $offset Start from a given offset in results.
     * @param int $limit Limit number of total results.
     *
     * @return Log
     */
    public function getLog($revisions = null, $paths = null, $offset = null, $limit = null)
    {
        return new Log($this, $revisions, $paths, $offset, $limit);
    }

    public function getDiff($revisions, $paths = []): Diff
    {
        if (null !== $revisions && !$revisions instanceof RevisionList) {
            $revisions = new RevisionList($this, $revisions);
        }

        $args = array_merge(['-r', '-p', '-M', '--no-commit-id', '--full-index', '--no-renames'], $revisions->getAsTextArray());

        if (!empty($paths)) {
            $args = array_merge($args, ['--'], (array) $paths);
        }

        $diff = Diff::parse($this->run('diff', $args));
        $diff->setRepository($this);

        return $diff;
    }

    /**
     * Returns the size of repository, in kilobytes.
     *
     * @return int A sum, in kilobytes
     */
    public function getSize()
    {
        $totalBytes = 0;
        $path       = realpath($this->gitDir);
        if ($path && file_exists($path)) {
            foreach (new RecursiveIteratorIterator(new RecursiveDirectoryIterator($path, FilesystemIterator::SKIP_DOTS)) as $object) {
                $totalBytes += $object->getSize();
            }
        }

        return (int) ($totalBytes / 1000 + 0.5);
    }

    /**
     * Executes a shell command on the repository, using PHP pipes.
     *
     * @param string $command The command to execute
     * @param array $env
     */
    public function shell($command, array $env = [])
    {
        $argument = sprintf('%s \'%s\'', $command, $this->gitDir);

        $prefix = '';
        foreach ($env as $name => $value) {
            $prefix .= sprintf('export %s=%s;', escapeshellarg($name), escapeshellarg($value));
        }

        proc_open($prefix . 'git shell -c ' . escapeshellarg($argument), [STDIN, STDOUT, STDERR], $pipes);
    }

    /**
     * Returns the hooks object.
     *
     * @return Hooks
     */
    public function getHooks()
    {
        return new Hooks($this);
    }

    public function getRemote()
    {
        return new Remote($this);
    }

    /**
     * Returns description of repository from description file in git directory.
     *
     * @return string The description
     */
    public function getDescription()
    {
        $file   = $this->gitDir . '/description';
        $exists = is_file($file);

        if (false === $exists) {
            return static::DEFAULT_DESCRIPTION;
        }

        return file_get_contents($this->gitDir . '/description');
    }

    /**
     * Tests if repository has a custom set description.
     *
     * @return bool
     */
    public function hasDescription()
    {
        return static::DEFAULT_DESCRIPTION !== $this->getDescription();
    }

    /**
     * Changes the repository description (file description in git-directory).
     *
     * @param $description
     * @return Repository the current repository
     */
    public function setDescription($description)
    {
        $file = $this->gitDir . '/description';

        file_put_contents($file, $description);

        return $this;
    }

    public function getStatus()
    {
        return new Status($this);
    }

    public function pull($branch = null, $remote = 'origin', $args = [], $options = [])
    {
        $options = array_merge([
            'callback' => null,
        ], $options);

        if (empty($branch)) {
            $branch = $this->getCurrentBranch();
        }

        array_unshift($args, $remote, $branch);

        if ($options['callback']) {
            $args[] = '--progress';
        }

        try {
            $this->run('pull', $args, $options);
        } catch (ProcessException) {
            return false;
        }
        return true;
    }

    public function add(array $file = null, $force = false): Repository
    {
        $args = [];
        if ($force) {
            $args[] = '--force';
        }
        if ($file !== null) {
            $args[] = '--';
            $args   = array_merge($args, $file);
        } else {
            $args[] = '--all';
        }

        $this->run('add', $args);
        return $this;
    }

    public function move($fromPath, $toPath, $force = false): Repository
    {
        $args = [];
        if ($force) {
            $args[] = '--force';
        }
        $args[] = $fromPath;
        $args[] = $toPath;
        $this->run('mv', $args);
        return $this;
    }

    public function remove(array $file, $recursive = false, $force = false): Repository
    {
        $args = [];
        if ($recursive) {
            $args[] = '-r';
        }
        if ($force) {
            $args[] = '--force';
        }
        $args[] = '--';
        $args   = array_merge($args, $file);

        $this->run('rm', $args);
        return $this;
    }

    public function show(...$args)
    {
        return $this->run('show', $args);
    }

    public function isDirty(): bool
    {
        return !$this->isClean();
    }

    public function isClean(): bool
    {
        return $this->getStatus()->isClean();
    }

    public function push($branch = null, $remote = 'origin', $force = false, $options = [])
    {
        $options = array_merge([
            'callback' => null,
        ], $options);

        if (empty($branch)) {
            $branch = $this->getCurrentBranch();
        }
        $args = [$remote, $branch];

        if ($options['callback']) {
            $args[] = '--progress';
        }

        if ($force) {
            $args[] = '-f';
        }

        $this->run('push', $args, $options);
    }

    /**
     * This command is a facility command. You can run any command
     * directly on git repository.
     *
     * @param string $command Git command to run (checkout, branch, tag)
     * @param array $args Arguments of git command
     *
     * @return string Output of a successful process or null if execution failed and debug-mode is disabled.
     * @throws RuntimeException Error while executing git command (debug-mode only)
     *
     */
    public function run($command, $args = [], $options = [])
    {
        $options = array_merge([
            'command'     => $this->command,
            'environment' => $this->environment,
            'timeout'     => $this->timeout,
            'callback'    => null,
        ], $options);

        $cwd = $this->gitDir;

        if ($this->workingDir) {
            $cwd = $this->workingDir;
        }

        $base[] = $command;

        $process = static::getProcess($base, $args, $options, $cwd);

        try {
            if ($this->lock) {
                flock($this->lock, LOCK_EX);
            }
            $process->run(function ($type, $buffer) use ($process, $options) {
                if ($options['callback'] && call_user_func($options['callback'], $buffer, $type)) {
                    $process->stop();
                }
            });

            self::trigger($process);

            if (!$process->isSuccessful()) {
                throw new ProcessException($process);
            }

            return $process->getOutput();
        } finally {
            if ($this->lock) {
                flock($this->lock, LOCK_UN);
            }
        }
    }

    public function __destruct()
    {
        if ($this->lock) {
            fclose($this->lock);
        }
    }

    public static function listen($listener, $replace = true)
    {
        if ($replace) {
            self::$listeners = [];
        }
        self::$listeners[] = $listener;
    }

    protected static function trigger(Process $process)
    {
        foreach (self::$listeners as $listener) {
            $listener($process);
        }
    }

    /**
     * Initializes a repository and returns the instance.
     *
     * @param string $path path to the repository
     * @param bool $bare indicate to create a bare repository
     * @param array $options options for Repository creation
     *
     * @return Repository
     * @throws RuntimeException Directory exists or not writable (only if debug=true)
     *
     */
    public static function init($path, $bare = true, array $args = [], array $options = [])
    {
        $process = static::getProcess('init', array_merge(['-q'], $bare ? ['--bare'] : [], $args, [$path]), $options);

        $process->run();

        self::trigger($process);

        if (!$process->isSuccessFul()) {
            throw new ProcessException($process);
        }

        return new static($path);
    }

    /**
     * Checks the validity of a git repository url without cloning it.
     *
     * This will use the `ls-remote` command of git against the given url.
     * Usually, this command returns 0 when successful, and 128 when the
     * repository is not found.
     *
     * @param string $url url of repository to check
     * @param array $options options for Repository creation
     *
     * @return bool true if url is valid
     */
    public static function isValid($url, array $options = [])
    {
        $process = static::getProcess('ls-remote', [$url], $options);

        $process->run();

        self::trigger($process);

        return $process->isSuccessFul();
    }

    /**
     * Internal method to launch effective ``git clone`` command.
     *
     * @param string $path indicates where to clone repository
     * @param string $url url of repository to clone
     * @param array $args arguments to be added to the command-line
     * @param array $options
     * @return Repository
     */
    public static function clone($path, $url, array $args = [], $options = [])
    {
        $options = array_merge([
            'timeout'  => 0,
            'callback' => null,
        ], $options);

        if ($options['callback']) {
            $args[] = '--progress';
        }

        $process = static::getProcess('clone', array_merge($args, [$url, $path]), $options);

        $process->run(function ($type, $buffer) use ($process, $options) {
            if ($options['callback'] && $options['callback']($buffer, $type)) {
                $process->stop();
            }
        });

        self::trigger($process);

        if (!$process->isSuccessFul()) {
            throw new ProcessException($process);
        }

        return new static($path);
    }

    /**
     * This internal method is used to create a process object.
     *
     * Made private to be sure that process creation is handled through the run method.
     * run method ensures logging and debug.
     *
     * @param       $command
     * @param array $args
     * @param array $options
     * @return Process
     */
    private static function getProcess($command, $args = [], array $options = [], $cwd = null)
    {
        $options = array_merge([
            'environment' => [],
            'command'     => 'git',
            'timeout'     => 3600,
        ], $options);
        $process = new Process([$options['command'], ...(array) $command, ...$args], $cwd);
        $process->setEnv($options['environment']);
        $process->setTimeout($options['timeout']);
        $process->setIdleTimeout($options['timeout']);

        return $process;
    }
}
