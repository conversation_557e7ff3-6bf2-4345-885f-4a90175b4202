<?php

namespace topthink\git;

use JsonSerializable;
use topthink\git\parser\StatusParser;

class Status implements JsonSerializable
{
    /**
     * @var Repository
     */
    protected $repository;

    /**
     * @var array
     */
    public $files;

    public $branch;

    protected $unmerged;

    public function __construct(Repository $repository)
    {
        $this->repository = $repository;
        $this->initialize();
    }

    protected function getData()
    {
        $output = $this->repository->run('status', ['--branch', '--porcelain=v2', '--no-renames']);

        $parser = new StatusParser();
        $parser->parse($output);
        $this->files  = $parser->files;
        $this->branch = $parser->branch;
    }

    protected function initialize()
    {
        $this->getData();

        //add untracked
        $untrackedFiles = $this->getUntrackedFiles();

        if (!empty($untrackedFiles)) {
            $this->repository->add(array_map(static function ($file) {
                return $file['filename'];
            }, $untrackedFiles));
            $this->initialize();
        }
    }

    public function getUntrackedFiles()
    {
        return array_filter($this->files, static function ($file) {
            return $file['type'] === '?';
        });
    }

    public function getChanges()
    {
        $changes = [];
        foreach ($this->files as $file) {
            if ($file['type'] == 'u') {
                $this->unmerged = true;
            }
            $status = $file['x'] . $file['y'];
            $name   = $file['filename'];
            if (preg_match('/(M[.M])|([.M]M)/', $status)) {
                $changes[$name] = 'M';
            } elseif (preg_match('/(A[.M])/', $status)) {
                $changes[$name] = 'A';
            } elseif (preg_match('/([.M]D)|(D[.])/', $status)) {
                $changes[$name] = 'D';
            } elseif (preg_match('/(DD)|(AU)|(UD)|(UA)|(DU)|(AA)|(UU)/', $status)) {
                $changes[$name] = 'U';
            } elseif ($file['type'] === '?') {
                if (isset($changes[$name]) && $changes[$name] === 'D') {
                    $changes[$name] = 'M';
                } else {
                    $changes[$name] = 'A';
                }
            }
        }
        return $changes;
    }

    public function isClean()
    {
        return empty($this->files);
    }

    #[\ReturnTypeWillChange]
    public function jsonSerialize()
    {
        return [
            'changes'  => $this->getChanges(),
            'branch'   => $this->branch,
            'unmerged' => $this->unmerged,
        ];
    }
}
