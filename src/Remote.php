<?php

namespace topthink\git;

class Remote
{
    /**
     * @var Repository
     */
    protected $repository;

    /**
     * @var Repository
     */
    public function __construct(Repository $repository)
    {
        $this->repository = $repository;
    }

    public function add($name, $url, $args = [])
    {
        $args = array_merge(['add'], $args, [$name, $url]);
        return $this->repository->run('remote', $args);
    }
}
