<?php

namespace topthink\git;

use topthink\git\exception\ProcessException;
use topthink\git\exception\ReferenceNotFoundException;

abstract class Reference extends Revision
{
    protected $commitHash;

    public function __construct(Repository $repository, $revision, $commitHash = null)
    {
        parent::__construct($repository, $revision);
        $this->commitHash = $commitHash;
    }

    public function getFullname()
    {
        return $this->revision;
    }

    public function delete()
    {
        $this->repository->getReferences()->delete($this->getFullname());
    }

    public function getCommitHash()
    {
        if (null !== $this->commitHash) {
            return $this->commitHash;
        }

        try {
            $result = $this->repository->run('rev-parse', ['--verify', $this->revision]);
        } catch (ProcessException $e) {
            throw new ReferenceNotFoundException(sprintf('Can not find revision "%s"', $this->revision));
        }

        return $this->commitHash = trim($result);
    }

    /**
     * Returns the commit associated to the reference.
     *
     * @return Commit
     */
    public function getCommit()
    {
        return $this->repository->getCommit($this->getCommitHash());
    }

    public function getLastModification($path = null)
    {
        return $this->getCommit()->getLastModification($path);
    }
}
