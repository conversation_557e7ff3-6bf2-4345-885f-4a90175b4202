<?php

namespace topthink\git\blame;

use topthink\git\Commit;

class Line
{
    /**
     * @var Commit
     */
    protected $commit;
    protected $sourceLine;
    protected $targetLine;
    protected $blockLine;
    protected $content;

    /**
     * Instanciates a new Line object.
     * @param Commit $commit
     * @param $sourceLine
     * @param $targetLine
     * @param $blockLine
     * @param $content
     */
    public function __construct(Commit $commit, $sourceLine, $targetLine, $blockLine, $content)
    {
        $this->commit     = $commit;
        $this->sourceLine = $sourceLine;
        $this->targetLine = $targetLine;
        $this->blockLine  = $blockLine;
        $this->content    = $content;
    }

    public function getContent()
    {
        return $this->content;
    }

    public function getLine()
    {
        return $this->sourceLine;
    }

    public function getCommit()
    {
        return $this->commit;
    }
}
