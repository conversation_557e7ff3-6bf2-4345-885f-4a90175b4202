<?php

namespace topthink\git;

use topthink\git\exception\InvalidArgumentException;

class TreeEntry implements \JsonSerializable
{
    public function __construct(protected Repository $repository, public $mode, public $type, public $hash, public $name)
    {

    }

    public function isTree()
    {
        return $this->type == 'tree';
    }

    public function getTree()
    {
        if ($this->isTree()) {
            return $this->repository->getTree($this->hash);
        }
        throw new InvalidArgumentException('This is not a tree');
    }

    #[\ReturnTypeWillChange]
    public function jsonSerialize()
    {
        $arr = [
            'mode' => $this->mode,
            'type' => $this->type,
            'hash' => $this->hash,
            'name' => $this->name,
        ];

        if ($this->isTree()) {
            $arr['children'] = $this->getTree()->getEntries();
        }

        return $arr;
    }
}
