<?php

namespace topthink\git;

use topthink\git\exception\InvalidArgumentException;
use topthink\git\exception\UnexpectedValueException;

class Tree
{
    protected $repository;
    protected $hash;
    protected $isInitialized = false;

    /** @var TreeEntry[] */
    protected $entries;

    public function __construct(Repository $repository, $hash)
    {
        $this->repository = $repository;
        $this->hash       = $hash;
    }

    public function getHash()
    {
        return $this->hash;
    }

    protected function initialize()
    {
        if (true === $this->isInitialized) {
            return;
        }

        $output = $this->repository->run('cat-file', ['-p', $this->hash]);
        $parser = new parser\TreeParser();
        $parser->parse($output);

        $this->entries = [];

        foreach ($parser->entries as $entry) {
            [$mode, $type, $hash, $name] = $entry;

            $this->entries[$name] = new TreeEntry($this->repository, $mode, $type, $hash, $name);
        }

        $this->isInitialized = true;
    }

    public function getEntries()
    {
        $this->initialize();

        return $this->entries;
    }

    public function getEntry($name)
    {
        $this->initialize();

        if (!isset($this->entries[$name])) {
            throw new InvalidArgumentException('No entry ' . $name);
        }

        return $this->entries[$name];
    }

    public function resolvePath($path)
    {
        if ($path == '') {
            return $this;
        }

        $path = preg_replace('#^/#', '', $path);

        $segments = explode('/', $path);
        $element  = $this;
        foreach ($segments as $segment) {
            if ($element instanceof self) {
                $element = $element->getEntry($segment);
            } elseif ($element instanceof Blob) {
                throw new InvalidArgumentException('Unresolvable path');
            } else {
                throw new UnexpectedValueException('Unknow type of element');
            }
        }

        return $element;
    }
}
