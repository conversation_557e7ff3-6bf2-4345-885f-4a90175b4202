<?php

namespace app\lib\job;

use app\job\MarkReleaseFailed;
use app\lib\Job;
use app\model\Release;
use think\facade\Queue;

class ReleaseJob extends Job
{
    public static function create(Release $release, $type, $timeout = 300)
    {
        if (self::supportK8sJob()) {
            //k8s job 模式
            $args   = ['think', 'book:pack', '--timeout', "{$timeout}", "{$release->id}", "{$type}"];
            $labels = [
                'topwrite/release-id' => (string) $release->id,
                'topwrite/book-id'    => (string) $release->book->hash_id,
            ];
            self::createK8sJob('release-job-', $args, $labels, $timeout);
        } else {
            Queue::push('PackJob', [
                'id'      => $release->id,
                'type'    => $type,
                'timeout' => $timeout,
            ], 'release');
        }

        Queue::later($timeout + 10, MarkReleaseFailed::class, [$release->id, $type]);
    }
}
