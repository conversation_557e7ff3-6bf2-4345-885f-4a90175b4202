<?php

namespace app\lib\job;

use app\lib\Job;

class ExportJob extends Job
{
    public static function create($id, $type, $timeout = 300)
    {
        if (self::supportK8sJob()) {
            //k8s job 模式
            $args   = ['think', 'book:export', $id, $type];
            $labels = [
                'topwrite/export-id' => (string) $id,
            ];
            self::createK8sJob('export-job-', $args, $labels, $timeout);
        }
    }
}
