<?php

namespace app\lib;

use app\lib\mcp\Tool;
use app\model\Conversation;
use app\model\Message;
use app\model\Space;
use app\repo\tools\Create;
use app\repo\tools\Delete;
use app\repo\tools\Download;
use app\repo\tools\Grep;
use app\repo\tools\Insert;
use app\repo\tools\Rename;
use app\repo\tools\StrReplace;
use app\repo\tools\View;
use app\repo\Workspace;
use think\agent\Util;
use think\ai\Client;
use think\helper\Arr;

class Agent extends \think\agent\Agent
{
    const PROMPT = <<<EOT
你是一个智能写作助理，使用以下说明和可用的工具来帮助用户完成文档写作任务。
文档是指由多个章节文件通过SUMMARY.md或SUMMARY.json组织起来的一个集合

# 文档结构
- logo.png 文档logo文件
- book.json 文档配置文件
- SUMMARY.md或SUMMARY.json 目录文件，通过该文件组织整个文档的结构，所有的章节都需要在该文件中引用
- .topwrite/assets/* 用于存放图片、视频等资源文件的目录
- .topwrite/style.css 定义文档内容的样式文件，仅可使用p,li,ul等正文部分的选择器
- **/*.md 章节文件

> 以上这些文件或文件夹都可能不存在，可以创建后使用

## 目录文件格式(不包含<summary>标签)

### markdown格式
- 无分组：
<summary>
* [章节一](chapter1.md)
    * [章节一-1](chapter1-1.md)
* [章节二](chapter2.md)
</summary>

- 有分组：
<summary>
## 分组一
* [章节一](chapter1.md)
    * [章节一-1](chapter1-1.md)
* [章节二](chapter2.md)

## 分组二
* [章节三](chapter3.md)
</summary>

### json格式
只有需要设置章节的元数据的时候才会采用json格式,根节点只有一个元素且分组标题为空时表示无分组
<summary>
[
    {
        "title": "分组一",
        "articles": [
            {
                "title": "章节一",
                "ref": "chapter1.md",
                "children":[
                    {
                        "title": "章节一-1",
                        "ref": "chapter1-1.md"
                    }
                ]
            },
            {
                "title": "章节二",
                "ref": "chapter2.md"
            }
        ]
    },
    {
        "title": "分组二",
        "metadata": {
            "reference": "4x8b5xgdyp"
        },
        "articles": [
            {
                "title": "章节三",
                "ref": "chapter3.md",
                "metadata": {
                    "icon": "icon.png"
                },
            }
        ]
    }
]
</summary>

## 章节文件格式
不需要在章节开头添加标题
章节使用Github-flavored Markdown格式编写，禁止直接使用html标签
图片等资源文件均放在.topwrite/assets/目录下，章节内使用相对路径引用，如：![](.topwrite/assets/image.png)
列表使用`*` 
并扩展了以下语法：
- `> [info|success|danger|warning] some text`   //扩展了引用的语法，使用不同的背景以及字体颜色显示
- `:-: some text`  //表示居中对齐
- `--: some text`  //表示右对齐
- `^`   //独立一行表示空行

<important>
当用户有创建章节或文档的需求时，应当先在目录文件中创建引用并放到合适的位置
</important>

# 语气和风格
你应该简洁、直接、切中要点。
除非用户要求详细说明，否则你的回答必须控制在4行以内（不包括工具使用）。
重要：在保持帮助性、质量与准确性的前提下，尽量减少输出标记。仅处理具体的查询或任务，避免无关信息，除非对完成请求至关重要。如果你能用1至3句话或一个简短段落回答，请这么做。
重要：除非用户要求，否则不要添加不必要的前言或后语。完成文件操作后，请直接停止，而不是提供解释。
请直接回答用户的问题，无需展开、解释或细节。最佳回答是一字不多。避免引入、结论和解释。你必须避免在回答前后添加额外文本，如“答案是……。”、“这是文件的内容……”或“根据提供的信息，答案是……”或“这是我接下来要做的事……”。
输出文本以与用户通信；你在工具使用之外输出的所有文本都将显示给用户。仅使用工具来完成任务。
如果你不能或不愿意帮助用户，请不要说明原因或可能导致的后果，因为这会显得说教和令人讨厌。如果可能，请提供有帮助的替代方案，否则将你的回复控制在1至2句话内。
仅当用户明确要求时才使用表情符号。除非被要求，否则在所有通信中避免使用表情符号。

# 限制
一次不要处理太长的内容，如果章节内容过长，可以分段处理

# 主动性
你被允许具有主动性，但仅当用户要求你做某事时。你应该努力在以下之间取得平衡：
- 在被问及如何做某事时，尽最大努力回答问题，然后不立即采取行动。
例如，如果用户问你如何处理某事，你应该首先尽力回答他们的问题，而不是立即采取行动。
EOT;

    /** @var Conversation */
    protected $conversation;

    /** @var Message */
    protected $message;

    protected $canUseTool = true;

    /** @var Client */
    protected $client;

    public function __construct(protected Space $space, protected Workspace $workspace)
    {
    }

    protected function initConversation($params)
    {
        $input          = Arr::get($params, 'input');
        $conversationId = Arr::get($params, 'conversation');

        if (!empty($conversationId)) {
            if ($conversationId instanceof Conversation) {
                $conversation = $conversationId;
            } else {
                $conversation = $this->space->conversations()->find($conversationId);
            }
        }

        if (empty($conversation)) {
            $conversation = $this->space->conversations()->save([
                'title' => Arr::get($input, 'query'),
            ]);
        } else {
            $conversation->save(['update_time' => Date::now()]);
        }

        $this->conversation = $conversation;

        $this->message = $this->conversation->messages()->save([
            'space_id' => $this->space->id,
            'input'    => $input,
        ]);
    }

    protected function initTools($params)
    {
        //文本编辑工具
        $this->addFunction('view', new View($this->workspace));
        $this->addFunction('str_replace', new StrReplace($this->workspace));
        $this->addFunction('create', new Create($this->workspace));
        $this->addFunction('insert', new Insert($this->workspace));
        $this->addFunction('download', new Download($this->workspace));
        $this->addFunction('rename', new Rename($this->workspace));
        $this->addFunction('delete', new Delete($this->workspace));

        //搜索工具
        $this->addFunction('grep', new Grep($this->workspace));

        //内置插件
        $tools = Arr::get($params, 'tools', []);
        foreach ($tools as $tool) {
            $this->addPlugin($tool['plugin'], $tool['name'], $tool['args'] ?? []);
        }

        //mcp
        $mcp = Arr::get($params, 'mcp', []);
        foreach ($mcp as $server) {
            $client = new \app\lib\mcp\Client($server['url'], $server['type'] ?? 'http', $server['headers'] ?? null);

            $tools   = $client->listTools();
            $allowed = $server['allowed'] ?? null;

            foreach ($tools as $key => $tool) {
                if (!empty($allowed) && !in_array($tool['name'], $allowed)) {
                    continue;
                }
                $this->addFunction("mcp-{$key}-{$tool['name']}", new Tool($client, $tool));
            }
        }
    }

    protected function buildPromptMessages()
    {
        $promptMessages = [];

        $system = [
            [
                'type' => 'text',
                'text' => self::PROMPT,
            ],
        ];

        $guidelines = $this->workspace->readFile('.topwrite/rules.md');

        if (!empty($guidelines)) {
            $system[] = [
                'type' => 'text',
                'text' => <<<EOT
<guidelines>
{$guidelines}
</guidelines>
EOT,
            ];
        }

        $promptMessages[] = [
            'role'    => 'system',
            'content' => $system,
        ];

        $historyMessages = $this->getHistoryMessages();
        $promptMessages  = array_merge($promptMessages, $historyMessages);

        $promptMessages[] = [
            'role'    => 'user',
            'content' => $this->message->content,
        ];

        return $promptMessages;
    }

    protected function getHistoryMessages()
    {
        $maxTokens = 96000;

        //获取历史记录
        /** @var \app\model\Message[] $messages */
        $messages = $this->conversation->messages()
            ->where('id', '<>', $this->message->id)
            ->order('create_time desc')
            ->cursor();

        $historyMessages = [];

        foreach ($messages as $message) {
            $chunkMessages = [
                [
                    'role'    => 'user',
                    'content' => $message->content,
                ],
            ];
            if (empty($message->output)) {
                $chunkMessages[] = [
                    'role'    => 'assistant',
                    'content' => 'None',
                ];
            } else {
                foreach ($message->output as $chunk) {
                    if (!empty($chunk['error'])) {
                        continue;
                    }
                    if (!empty($chunk['tools'])) {
                        $calls     = [];
                        $responses = [];
                        foreach ($chunk['tools'] as $tool) {
                            $content = !empty($tool['content']) ? json_encode($tool['content']) : ($tool['response'] ?? '');
                            if (!empty($content)) {
                                $calls[] = [
                                    'id'       => $tool['id'],
                                    'type'     => 'function',
                                    'function' => [
                                        'name'      => $tool['name'],
                                        'arguments' => $tool['arguments'],
                                    ],
                                ];

                                $responses[] = [
                                    'tool_call_id' => $tool['id'],
                                    'role'         => 'tool',
                                    'name'         => $tool['name'],
                                    'content'      => $content,
                                ];
                            }
                        }

                        $chunkMessages[] = [
                            'role'       => 'assistant',
                            'content'    => $chunk['content'] ?? null,
                            'tool_calls' => $calls,
                        ];

                        $chunkMessages = array_merge($chunkMessages, $responses);
                    } else {
                        $chunkMessages[] = [
                            'role'    => 'assistant',
                            'content' => empty($chunk['content']) ? 'None' : $chunk['content'],
                        ];
                    }
                }
            }

            $tempHistoryMessages = array_merge($chunkMessages, $historyMessages);

            $tokens = Util::tikToken($tempHistoryMessages);

            if ($tokens > $maxTokens * .6) {
                break;
            }

            $historyMessages = $tempHistoryMessages;
        }

        return $historyMessages;
    }

    protected function init($params)
    {
        $this->config = [
            'model' => [
                'name'     => 'glm-4.5',
                'thinking' => 'disabled',
                'params'   => [
                    'temperature' => 1,
                ],
            ],
            'user'  => md5("topwrite-{$this->space->hash_id}"),
        ];

        $this->initClient($params);
        $this->initConversation($params);
        $this->initTools($params);
    }

    protected function start()
    {
        yield ['conversation' => $this->conversation->id];
        yield ['id' => $this->message->id];
        yield from parent::start();
    }

    protected function complete()
    {
    }

    protected function saveChunks()
    {
        $this->message->save([
            'output' => $this->chunks,
        ]);
    }

    protected function initClient($params)
    {
        $token = Arr::get($params, 'token');
        if ($token) {
            $this->client = new Client($token);
        } else {
            $this->client = $this->space->getAiClient();
        }
    }

    protected function getClient(): Client
    {
        if (empty($this->client)) {
            throw new \RuntimeException('AI服务不可用');
        }

        return $this->client;
    }
}
