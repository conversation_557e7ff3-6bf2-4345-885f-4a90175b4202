<?php

namespace app\controller\space;

use Symfony\Component\Filesystem\Path;
use think\Filesystem;
use think\helper\Str;

class DownloadController extends Controller
{
    public function index(Filesystem $filesystem)
    {
        $type    = $this->request->param('type');
        $book    = $this->space->book;
        $release = $book->getLastRelease();
        $log     = $release->getLog($type);

        if (!$log->isSucceed()) {
            abort(404);
        }

        $filename = $filesystem->path(Path::join('artifact', $book->getArtifact($type)));

        $extension = strstr(pathinfo($filename)['basename'], '.');
        $sha       = Str::substr($release->sha, 0, 7);

        $title = $this->getWorkspace()->getConfig()->getValue('title') ?: 'book';

        return \think\swoole\helper\download($filename, "{$title}-{$sha}{$extension}");
    }
}
