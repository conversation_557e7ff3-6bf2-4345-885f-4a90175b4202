<?php

namespace app\controller\space\assistant;

use app\controller\space\Controller;
use app\lib\mcp\Client;

class <PERSON>cpController extends Controller
{
    public function tools()
    {
        $data = $this->validate([
            'url'     => 'require',
            'type'    => '',
            'headers' => '',
        ]);

        $client = new Client($data['url'], $data['type'] ?? 'http', $data['headers'] ?? null);

        $result = $client->listTools();

        return json($result);
    }
}
