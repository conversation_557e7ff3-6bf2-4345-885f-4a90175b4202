<?php

namespace app\controller\space;

use think\Cache;

class ExportController extends Controller
{
    public function save($id)
    {
        $data = $this->validate([
            'type'    => 'require',
            'article' => 'require',
        ]);

        $workspace = $this->getWorkspace();

        $this->space->createExportJob($id, $data['type'], $data['article'], $workspace->getWorkingDir());
    }

    public function download(Cache $cache, $id)
    {
        $export = $cache->get("export#{$id}");
        if (empty($export)) {
            abort(404);
        }

        $ext = match ($export['type']) {
            'pdf' => '.pdf',
            'word' => '.docx',
        };

        $filename = runtime_path('temp/export') . $id . $ext;

        if (!file_exists($filename)) {
            abort(404);
        }

        $name = $this->request->get('download', 'article');

        return \think\swoole\helper\download($filename, $name . $ext);
    }
}
