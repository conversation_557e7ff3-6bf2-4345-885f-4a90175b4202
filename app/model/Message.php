<?php

namespace app\model;

use think\helper\Arr;
use think\Model;

/**
 * Class app\model\Message
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $update_time
 * @property array $output
 * @property int $conversation_id
 * @property int $id
 * @property int $latency
 * @property int $space_id
 * @property int $usage
 * @property array $input
 * @property mixed $content
 */
class Message extends Model
{
    protected $jsonAssoc = true;

    protected $json = ['input', 'output'];

    protected function getContentAttr()
    {
        $content = [];

        $context = Arr::get($this->input, 'context');
        if (!empty($context)) {
            $reminder = "当前用户选择文件：{$context['filename']}";
            if (!empty($context['range'])) {
                $range    = $context['range'];
                $reminder .= "\n";
                if (!empty($range['start']) && !empty($range['end'])) {
                    $reminder .= "选择的范围为：L{$range['start']['line']}C{$range['start']['column']}-L{$range['end']['line']}C{$range['end']['column']}";
                    $reminder .= "\n";
                }
                $reminder .= "选择的文字为：\n";
                $reminder .= $range['text'];
            }

            $content[] = [
                'type' => 'text',
                'text' => <<<EOT
<system-reminder>
As you answer the user's questions, you can use the following context:
{$reminder}

# important-instruction-reminders
Do what has been asked; nothing more, nothing less.
IMPORTANT: this context may or may not be relevant to your tasks. You should not respond to this context unless it is highly relevant to your task.
</system-reminder>
EOT,
            ];
        }

        $query     = Arr::get($this->input, 'query');
        $content[] = [
            'type' => 'text',
            'text' => $query,
        ];

        return $content;
    }
}
