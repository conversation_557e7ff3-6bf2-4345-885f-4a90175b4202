<?php

namespace app\repo\message;

use topthink\git\Repository;
use app\repo\Message;

class Release extends Message
{

    public function index($page)
    {
        return $this->book->releases()->with('user')->order('id desc')->paginate(['page' => $page ?? 1], true);
    }

    public function read($id)
    {
        return $this->book->releases()->with(['logs', 'user'])->append(['is_latest'])->findOrFail($id);
    }

    public function last()
    {
        return $this->book->getLastRelease()->append(['logs'], true);
    }

    public function status()
    {
        $status = $this->workspace->getStatus();

        if ($status->branch['ab'][0] > 0) {
            throw new Exception('有未完成的提交');
        }

        $release    = $this->book->getLastRelease();
        $headCommit = $this->repo->getHeadCommit();
        if ($release) {
            if ($release->sha == $headCommit->getHash()) {
                throw new Exception('当前已是最新版本');
            }
            try {
                $base = $this->repo->getCommit($release->sha)->getTreeHash();
            } catch (\Exception) {

            }
        }

        if (empty($base)) {
            $base = Repository::EMPTY_TREE_SHA1;
        }

        $diff = $this->repo->getDiff([$base, $headCommit->getHash()]);

        return [
            'sha'   => [$base, $headCommit->getHash()],
            'files' => $diff->toArray()['files'],
        ];
    }

    public function save($data)
    {
        $headCommit = $this->repo->getHeadCommit();

        $this->space->createReleaseJob($headCommit->getHash(), $data['message'] ?? null);
    }

    public function retry($id, $type)
    {
        $release = $this->book->getLastRelease();
        if ($id != $release->id) {
            throw new Exception('仅最新的发布任务可以重试');
        }

        $log = $release->getLog($type, false);
        if ($log) {
            if (!$log->canRetry()) {
                throw new Exception('当前状态不可重试');
            }
            $log->retry([
                'space_id' => $this->space->id,
            ]);
        } else {
            $release->logs()->save([
                'space_id' => $this->space->id,
                'type'     => $type,
            ]);
        }
    }
}
