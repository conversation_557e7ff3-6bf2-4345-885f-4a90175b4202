<?php

namespace app\repo\message;

use app\model\BookMember;
use app\repo\Controller;
use app\repo\Message;
use ArrayObject;

class Workspace extends Message
{

    public function reset()
    {
        $this->space->reset();
    }

    public function getStatus()
    {
        //检查仓库
        $ready = $this->space->checkRepo();

        return [
            'version' => Controller::VERSION,
            'path'    => $this->client->getPath(),
            'ready'   => $ready,
        ];
    }

    public function getState()
    {
        $this->space->refresh();
        $workspace   = $this->client->getWorkspace(true);
        $accessLevel = $this->space->getAccessLevel();

        return [
            'id'           => $this->book->hash_id,
            'metadata'     => new ArrayObject($this->book->metadata ?? []),
            'version'      => Controller::VERSION,
            'path'         => "{$this->user->id}/{$this->book->id}",
            'workspace'    => [
                'current'     => $workspace->getCurrent(),
                'status'      => $workspace->getStatus(),
                'treeEntries' => [],
                'user'        => [
                    'name'        => $this->user->name,
                    'avatar'      => $this->user->avatar,
                    'tourVersion' => $this->user->tour_version ?? new ArrayObject(),
                ],
                'release'     => 'idle',
                'fetching'    => false,
            ],
            'options'      => [
                'preview'   => '/preview',
                'download'  => '/download',
                'import'    => '/import',
                'export'    => '/export',
                'asset'     => '/asset',
                'assistant' => [
                    'base'       => '/assistant',
                    'authorized' => !!$this->space->getAiClient(),
                    'tools'      => [
                        ['plugin' => 'metaso', 'name' => 'metasoSearchWeb', 'title' => '网页搜索', 'description' => '网页搜索工具'],
                        ['plugin' => 'metaso', 'name' => 'metasoSearchPic', 'title' => '图片搜索', 'description' => '图片搜索工具'],
                        ['plugin' => 'metaso', 'name' => 'metasoRead', 'title' => '读取网页内容', 'description' => '网页内容读取工具'],
                        ['plugin' => 'artist', 'name' => 'jimeng', 'title' => '即梦绘图', 'description' => 'AI 绘图工具', 'args' => ['output' => 'llm']],
                    ],
                ],
                'feedback'  => env('FEEDBACK_URL'),
                'lfs'       => $this->space->supportLfs(),
                'release'   => $accessLevel >= BookMember::MAINTAINER,
            ],
            'pluginCenter' => [
                'host'   => config('app.plugins_host'),
                'preset' => $this->space->getPresetPlugins(),
            ],
        ];
    }

    public function ready()
    {
        $this->workspace->init();
    }

    public function getConfig()
    {
        return $this->workspace->getConfig();
    }

    public function getSummary()
    {
        return $this->workspace->getSummary();
    }

    public function setSummary($summary)
    {
        $this->workspace->setSummary($summary);
    }

    public function setConfig($config)
    {
        $this->workspace->setConfig($config);
    }

    public function setCurrent($path)
    {
        $this->workspace->setCurrent($path);
    }

    public function setTourVersion($name, $version)
    {
        $this->user->save([
            'tour_version' => array_merge($this->user->tour_version ?? [], [$name => $version]),
        ]);
    }

    public function discard($path)
    {
        $this->workspace->discard($path);
    }

    public function commit($message, $release = false)
    {
        $result = $this->workspace->commit($message, true);

        if ($release && $result) {
            $headCommit = $this->repo->getHeadCommit();

            $this->space->createReleaseJob($headCommit->getHash(), 'Auto Release');
        }
    }

    public function sync()
    {
        $this->workspace->sync();
    }

    public function resolve($files)
    {
        $this->workspace->resolve($files);
    }
}
