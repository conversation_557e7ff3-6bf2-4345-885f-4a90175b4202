<?php

namespace app\repo;

use app\model\Space;
use app\repo\message\Exception as MessageException;
use Composer\Semver\Comparator;
use Exception;
use LogicException;
use think\App;
use think\helper\Str;
use think\Request;
use think\Session;
use think\swoole\websocket\Event;
use think\tracing\Tracer;
use Throwable;
use yunwuxin\Auth;

class Controller
{
    public const VERSION = '1.0.85';

    public function onConnect(App $app, Request $request, Auth $auth, Session $session, Tracer $tracer)
    {
        $scope = $tracer->startActiveSpan('connect');
        $span  = $scope->getSpan();

        try {
            if (empty($version = $request->param('version')) || Comparator::lessThan($version, self::VERSION . '-alpha')) {
                throw new LogicException('客户端版本与服务端不匹配，请刷新页面重新加载');
            }

            $space = Space::retrieve($request, $session, $auth);
            $space->touch();
            $client = $app->make(Client::class, [$space]);

            $span->setTag('path', $client->getPath());
            $client->setSpan($span);
        } catch (Exception $e) {
            $span->setTag('error', $e->getMessage());
            throw $e;
        } finally {
            $scope->close();
            $tracer->flush();
        }
    }

    public function onEvent(App $app, Event $event, Client $client, Tracer $tracer)
    {
        if (!empty($event->type)) {
            $scope = $tracer->startActiveSpan("event[{$event->type}]", [
                'child_of' => $client->getSpan(),
            ]);
            $span  = $scope->getSpan();
            try {
                [$class, $method] = explode('.', $event->type);

                $class = '\\app\\repo\\message\\' . Str::studly($class);
                try {
                    $message = $app->make($class);
                    $result  = $app->invoke([$message, $method], $event->data);
                    //如果有返回值，则使用原type推送
                    if ($result !== false) {
                        return [
                            'data' => $result,
                        ];
                    }
                } catch (MessageException $e) {
                    return [
                        'error' => $e,
                    ];
                }
            } catch (Throwable $e) {
                $span->setTag('error', $e->getMessage());
                $span->setTag('trace', $e->getTraceAsString());
                return [
                    'error' => $e->getMessage(),
                ];
            } finally {
                $client->setSpan($span);
                $scope->close();
                $tracer->flush();
            }
        }
    }

    public function onClose(?Client $client = null)
    {
        $client?->close();
    }

    public function onPong(?Client $client = null)
    {
        $client?->getSpace()->touch();
    }
}
