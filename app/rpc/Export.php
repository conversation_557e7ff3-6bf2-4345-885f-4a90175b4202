<?php

namespace app\rpc;

use think\Cache;
use think\swoole\concerns\WithMiddleware;
use think\swoole\rpc\Protocol;
use think\swoole\Websocket;
use think\swoole\rpc\File;

class Export
{
    use WithMiddleware;

    protected $export;

    public function __construct(protected Websocket $websocket, protected Cache $cache)
    {
        $this->middleware(function (Protocol $protocol, $next) {
            $context = $protocol->getContext();

            $this->export = $this->cache->get("export#{$context['id']}");

            if ($this->export) {
                return $next($protocol);
            }
        });
    }

    public function getOptions()
    {
        return $this->export['options'];
    }

    public function getResource(): \think\File
    {
        $root = $this->export['root'];
        $path = sys_get_temp_dir() . '/' . uniqid() . '.tar';
        $p    = new \PharData($path);
        $p->buildFromDirectory($root);
        return new File($path);
    }

    public function trace(string $message)
    {
        $this->websocket->to($this->export['client'])
            ->emit("export.{$this->export['id']}", [
                'type'    => 'trace',
                'message' => $message,
            ]);
    }

    public function failed(string $error)
    {
        $this->websocket->to($this->export['client'])
            ->emit("export.{$this->export['id']}", [
                'type'  => 'error',
                'error' => $error,
            ]);
    }

    public function succeed(\think\File $file)
    {
        $id   = $this->export['id'];
        $type = $this->export['type'];

        $ext = match ($type) {
            'pdf' => '.pdf',
            'word' => '.docx',
        };

        $file->move(runtime_path('temp/export'), $id . $ext);

        $this->websocket->to($this->export['client'])
            ->emit("export.{$id}", [
                'type' => 'succeed',
            ]);
    }
}
