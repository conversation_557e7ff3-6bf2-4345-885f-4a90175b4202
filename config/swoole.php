<?php

use app\middleware\SetRootDomain;
use app\repo\Controller;
use think\swoole\websocket\middleware\SessionInit;
use think\swoole\websocket\socketio\Handler;
use yunwuxin\auth\middleware\UseGuard;

return [
    'http'       => [
        'enable'     => true,
        'host'       => '0.0.0.0',
        'port'       => 8080,
        'worker_num' => swoole_cpu_num(),
        'options'    => [
            'package_max_length' => 20 * 1024 * 1024,
        ],
    ],
    'websocket'  => [
        'enable'        => true,
        'handler'       => Handler::class,
        'ping_interval' => 25000,
        'ping_timeout'  => 60000,
        'room'          => [
            'type'  => 'redis',
            'table' => [
                'room_rows'   => 8192,
                'room_size'   => 2048,
                'client_rows' => 4096,
                'client_size' => 2048,
            ],
            'redis' => [
                'host'          => env('REDIS_HOST', 'redis'),
                'port'          => 6379,
                'database'      => 1,
                'max_active'    => 10,
                'max_wait_time' => 5,
            ],
        ],
        'listen'        => [],
        'subscribe'     => [
            Controller::class,
        ],
        'middleware'    => [
            SetRootDomain::class,
            SessionInit::class,
            [UseGuard::class, ['space']],
        ],
    ],
    'rpc'        => [
        'server' => [
            'enable'   => true,
            'host'     => '0.0.0.0',
            'port'     => 9000,
            'services' => [
                \app\rpc\Release::class,
                \app\rpc\Import::class,
                \app\rpc\Export::class,
            ],
        ],
        'client' => [],
    ],
    'queue'      => [
        'enable'  => true,
        'workers' => [
            'default'   => [
                'timeout' => 300,
            ],
            'repo@long' => [
                'tries'   => 1,
                'timeout' => 20 * 60,
            ],
        ],
    ],
    'hot_update' => [
        'enable'  => env('APP_HOT', false),
        'type'    => 'find',
        'name'    => ['*.php', '*.twig'],
        'include' => [app_path(), root_path('view'), config_path(), root_path('route'), root_path('asset/dist/asset')],
        'exclude' => [],
    ],
    //连接池
    'pool'       => [
        'db'    => [
            'enable'        => true,
            'max_active'    => 20,
            'min_active'    => 5,
            'max_wait_time' => 5,
        ],
        'cache' => [
            'enable'        => true,
            'max_active'    => 20,
            'min_active'    => 5,
            'max_wait_time' => 5,
        ],
    ],
    'ipc'        => [
        'type'  => 'redis',
        'redis' => [
            'host'          => env('REDIS_HOST', 'redis'),
            'port'          => 6379,
            'database'      => 1,
            'max_active'    => 5,
            'max_wait_time' => 5,
        ],
    ],
    //锁
    'lock'       => [
        'enable' => true,
        'type'   => 'table',
        'redis'  => [
            'host'          => '127.0.0.1',
            'port'          => 6379,
            'max_active'    => 3,
            'max_wait_time' => 5,
        ],
    ],
    'tables'     => [],
    //每个worker里需要预加载以共用的实例
    'concretes'  => [],
    //重置器
    'resetters'  => [],
    //每次请求前需要清空的实例
    'instances'  => [],
    //每次请求前需要重新执行的服务
    'services'   => [],
];
