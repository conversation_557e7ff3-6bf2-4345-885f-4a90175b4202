{"name": "topthinkcloud/client", "type": "library", "license": "Apache-2.0", "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php-http/cache-plugin": "^1.7.1", "php-http/client-common": "^2.3", "php-http/discovery": "^1.12", "php-http/httplug": "^2.2", "php-http/multipart-stream-builder": "^1.1.2", "psr/cache": "^1.0", "psr/http-client-implementation": "^1.0", "psr/http-factory-implementation": "^1.0", "psr/http-message": "^1.0", "symfony/options-resolver": ">=6.4", "symfony/polyfill-php80": "^1.17", "firebase/php-jwt": "^6.1"}, "require-dev": {"guzzlehttp/guzzle": "^7.2", "http-interop/http-factory-guzzle": "^1.0", "topthink/framework": "^6.0", "yunwuxin/think-notification": "^3.0"}, "autoload": {"psr-4": {"TopThinkCloud\\": "src"}}, "extra": {"think": {"services": ["TopThinkCloud\\Service"], "config": {"cloud": "src/config.php"}}}}