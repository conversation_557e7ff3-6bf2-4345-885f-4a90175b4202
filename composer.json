{"name": "topthink/think", "description": "the new thinkphp framework", "type": "project", "keywords": ["framework", "thinkphp", "ORM"], "homepage": "http://thinkphp.cn/", "license": "Apache-2.0", "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "require": {"php": ">=8.0", "ext-json": "*", "ext-inotify": "*", "ext-fileinfo": "*", "topthink/framework": "8.x-dev", "topthink/think-orm": "3.0.x-dev", "topthink/think-filesystem": "^1.0", "topthink/think-swoole": "4.0.x-dev", "topthink/think-workflow": "2.0.x-dev", "yunwuxin/think-social": "3.1.x-dev", "yunwuxin/think-twig": "^3.0", "yunwuxin/think-auth": "^3.0.x-dev", "topthink/think-queue": "3.0.x-dev", "composer/semver": "^1.5", "topthink/think-migration": "^3.0", "topthink/think-annotation": "2.0.x-dev", "nesbot/carbon": "^2.46", "yohang88/letter-avatar": "^2.2", "phpseclib/phpseclib": "^2.0", "symfony/filesystem": "^5.2", "symfony/finder": "^5.4", "diff/diff": "^3.2", "knplabs/github-api": "^3.0", "guzzlehttp/guzzle": "^7.0.1", "http-interop/http-factory-guzzle": "^1.0", "symfony/process": "^6.0", "aliyuncs/oss-sdk-php": "^2.4", "hashids/hashids": "^4.1", "m4tthumphrey/php-gitlab-api": "^11.4", "yunwuxin/think-cron": "3.0.x-dev", "topthinkcloud/client": "^1.1.6", "k8s/client": "^1.7", "k8s/http-guzzle": "^1.0", "spatie/url": "^2.0", "yunwuxin/think-chunk-upload": "^1.0.1", "topthink/gitlib": "dev-master", "firebase/php-jwt": "^6.2", "topthink/think-tracing": "^1.0", "jcchavezs/zipkin-opentracing": "^2.0", "topthink/think-agent": "dev-master", "topthink/think-ai": "dev-master"}, "require-dev": {"topthink/think-dumper": "^1.0", "topthink/think-ide-helper": "dev-master"}, "autoload": {"psr-4": {"app\\": "app"}}, "config": {"preferred-install": "dist", "platform-check": false, "platform": {"ext-swoole": "4.6.0", "ext-inotify": "3.0.0", "ext-fileinfo": "1.0.4"}, "allow-plugins": {"php-http/discovery": true}}, "scripts": {"post-autoload-dump": ["@php think service:discover", "@php think vendor:publish"]}, "repositories": [{"type": "vcs", "url": "https://git.topthink.com/topteam/gitlib.git"}]}